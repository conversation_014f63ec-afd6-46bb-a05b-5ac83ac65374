registry=https://registry.npmjs.org/
@alj-react:registry=https://artifactory.asia.axa-cloud.com/artifactory/api/npm/axa-li-jp_aljdevops-npm-release/
@alj-npm-utils:registry=https://artifactory.asia.axa-cloud.com/artifactory/api/npm/axa-li-jp_aljdevops-npm-release/
@axa-japan:registry=https://artifactory.asia.axa-cloud.com/artifactory/api/npm/axa-li-jp_aljdevops-npm-release/

# SSL configuration for corporate network
strict-ssl=false
ca=null

//artifactory.asia.axa-cloud.com/artifactory/api/npm/axa-li-jp_aljdevops-npm-release/:username=<EMAIL>
//artifactory.asia.axa-cloud.com/artifactory/api/npm/axa-li-jp_aljdevops-npm-release/:_password=QUtDcDhwUlFrRnVZS2hxcHI1dlBBOW5LRXl3dGdiZ3JUa2s3ZXpZTjFpYXVLVDFlZDc5amY3QkZoeFJYczlidTlNYlBFcURXUw==
//artifactory.asia.axa-cloud.com/artifactory/api/npm/axa-li-jp_aljdevops-npm-release/:always-auth=true


