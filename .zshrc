# If you come from bash you might have to change your $PATH.
# export PATH=$HOME/bin:/usr/local/bin:$PATH

# Path to your oh-my-zsh installation.
export ZSH="$HOME/.oh-my-zsh"

# Set name of the theme to load --- if set to "random", it will
# load a random theme each time oh-my-zsh is loaded, in which case,
# to know which specific one was loaded, run: echo $RANDOM_THEME
# See https://github.com/ohmyzsh/ohmyzsh/wiki/Themes
# ZSH_THEME="avit"
# ZSH_THEME="af-magic"

# Set list of themes to pick from when loading at random
# Setting this variable when ZSH_THEME=random will cause zsh to load
# a theme from this variable instead of looking in $ZSH/themes/
# If set to an empty array, this variable will have no effect.
# ZSH_THEME_RANDOM_CANDIDATES=( "robbyrussell" "agnoster" )

# Uncomment the following line to use case-sensitive completion.
# CASE_SENSITIVE="true"

# Uncomment the following line to use hyphen-insensitive completion.
# Case-sensitive completion must be off. _ and - will be interchangeable.
# HYPHEN_INSENSITIVE="true"

# Uncomment one of the following lines to change the auto-update behavior
# zstyle ':omz:update' mode disabled  # disable automatic updates
# zstyle ':omz:update' mode auto      # update automatically without asking
zstyle ':omz:update' mode reminder # just remind me to update when it's time

# Uncomment the following line to change how often to auto-update (in days).
# zstyle ':omz:update' frequency 13

# Uncomment the following line if pasting URLs and other text is messed up.
# DISABLE_MAGIC_FUNCTIONS="true"

# Uncomment the following line to disable colors in ls.
# DISABLE_LS_COLORS="true"

# Uncomment the following line to disable auto-setting terminal title.
# DISABLE_AUTO_TITLE="true"

# Uncomment the following line to enable command auto-correction.
# ENABLE_CORRECTION="true"

# Uncomment the following line to display red dots whilst waiting for completion.
# You can also set it to another string to have that shown instead of the default red dots.
# e.g. COMPLETION_WAITING_DOTS="%F{yellow}waiting...%f"
# Caution: this setting can cause issues with multiline prompts in zsh < 5.7.1 (see #5765)
# COMPLETION_WAITING_DOTS="true"

# Uncomment the following line if you want to disable marking untracked files
# under VCS as dirty. This makes repository status check for large repositories
# much, much faster.
# DISABLE_UNTRACKED_FILES_DIRTY="true"

# Uncomment the following line if you want to change the command execution time
# stamp shown in the history command output.
# You can set one of the optional three formats:
# "mm/dd/yyyy"|"dd.mm.yyyy"|"yyyy-mm-dd"
# or set a custom format using the strftime function format specifications,
# see 'man strftime' for details.
# HIST_STAMPS="mm/dd/yyyy"

# Would you like to use another custom folder than $ZSH/custom?
# ZSH_CUSTOM=/path/to/new-custom-folder

# Which plugins would you like to load?
# Standard plugins can be found in $ZSH/plugins/
# Custom plugins may be added to $ZSH_CUSTOM/plugins/
# Example format: plugins=(rails git textmate ruby lighthouse)
# Add wisely, as too many plugins slow down shell startup.
plugins=(git)

source $ZSH/oh-my-zsh.sh

# User configuration

# export MANPATH="/usr/local/man:$MANPATH"

# You may need to manually set your language environment
# export LANG=en_US.UTF-8

# Preferred editor for local and remote sessions
# if [[ -n $SSH_CONNECTION ]]; then
#   export EDITOR='vim'
# else
#   export EDITOR='mvim'
# fi

# Compilation flags
# export ARCHFLAGS="-arch x86_64"

# Set personal aliases, overriding those provided by oh-my-zsh libs,
# plugins, and themes. Aliases can be placed here, though oh-my-zsh
# users are encouraged to define aliases within the ZSH_CUSTOM folder.
# For a full list of active aliases, run `alias`.
#
# Example aliases
# alias zshconfig="mate ~/.zshrc"
# alias ohmyzsh="mate ~/.oh-my-zsh"

# export PATH="/usr/local/opt/node@16/bin:$PATH"
fpath+=("/opt/homebrew/share/zsh/site-functions")
autoload -U promptinit
promptinit

# zstyle :prompt:pure:path color 120
zstyle :prompt:pure:git:branch color 118

prompt pure

source /opt/homebrew/share/zsh-syntax-highlighting/zsh-syntax-highlighting.zsh
# export JAVA_HOME=`/usr/libexec/java_home`

export PATH=$PATH:/opt/homebrew/bin
export PATH="/usr/local/opt/openjdk@11/bin:$PATH"
export PATH=$HOME/.nodebrew/current/bin:$PATH
export PATH="$HOME/bin:$PATH"
export PATH="/usr/local/opt/libxslt/bin:$PATH"
export PATH="/usr/local/opt/ruby/bin:$PATH"
#THIS MUST BE AT THE END OF THE FILE FOR SDKMAN TO WORK!!!
export SDKMAN_DIR="$HOME/.sdkman"
[[ -s "$HOME/.sdkman/bin/sdkman-init.sh" ]] && source "$HOME/.sdkman/bin/sdkman-init.sh"
export CPPFLAGS="-I/usr/local/opt/openjdk@11/include"
export LDFLAGS="-L/usr/local/opt/libxslt/lib"
export CPPFLAGS="-I/usr/local/opt/libxslt/include"
export PKG_CONFIG_PATH="/usr/local/opt/libxslt/lib/pkgconfig"
# export PATH=/usr/local/opt/python/libexec/bin:$PATH

#ENV variables for development
# export REGION='ap-southeast-1'
# export CCIFA_BUCKET_NAME='axa-li-jp-ccifa-commission-bradley'
# export SFDC_AUTH_URL='https://test.salesforce.com/services/oauth2/token'
# export SFDC_CLIENT_ID='3MVG9Po2PmyYruulBDj.RmFHpzQjbtS3iMyPtNNXsfkeG2Q1X5FGIYUOJa9mCksMcnUFoTwAhGOZAwEtO8DkD'
# export SFDC_CLIENT_SECRET='****************************************************************'
# export SFDC_USERNAME='<EMAIL>.uat01'
# export SFDC_PASSWORD='Passw0rd'
# export EGRESS_APIGW='https://alj--uat01.sandbox.my.salesforce.com'
# export DB_HOST='shared-dev.cluster-cxiiqnufhuif.ap-southeast-1.rds.amazonaws.com'
# export DB_USERNAME='jp-ccifacom-batch-test2-user'
# export DB_PASSWORD='ZFG7u4PXz$Sh786ZXCg'
# export DB_NAME='jp-ccifacom-batch-test2-db'
export NODE_TLS_REJECT_UNAUTHORIZED=0
export GPG_TTY=$(tty)

alias dataloader="~/dataloader/v56.0.6/dataloader.app/Contents/MacOS/dataloader"
alias rebase="HUSKY=0 git pull --rebase origin test"
alias rebasemain="HUSKY=0 git pull --rebase origin main"
alias pushnew="HUSKY=0 git push origin head -u"
alias pushold="HUSKY=0 git push"
alias pushnewrebase="HUSKY=0 git push origin head -u --force-with-lease"
alias pusholdrebase="HUSKY=0 git push --force-with-lease"
alias deploy="npm run sls -- deploy -s bradley --verbose"
alias stashin="git stash"
alias stashout="git stash pop"
alias syncs3="aws s3 rm s3://axa-li-jp-ccifa-commission-bradley/output --recursive && aws s3 sync s3://axa-li-jp-ccifa-commission-dev/output s3://axa-li-jp-ccifa-commission-bradley/output"
alias pastetoken="pbpaste > ~/.aws/credentials"
alias ift='ssh ftpsq@10.239.8.92 -i ~/.ssh/ift_server.key'
alias iftdev='ssh bradley.wong@10.239.8.134 -i ~/.ssh/ift_server_dev.key'
alias resetauthor='git commit --amend --reset-author --no-edit && git rebase --continue'
alias undo='git reset --soft HEAD^'
alias pull='git stash && git pull & git stash pop'
alias downloadift='scp -p -i ~/.ssh/ift_server.key ftpsq@10.239.8.92:../IFT058/MONTHLY/20230913営業社員退職金計算書.csv ~/Desktop'
alias listift='ls -ltr'
alias migrate='serverless invoke --function DbMigration --stage bradley --data "{\"migrateType\": \"latest\"}" --log'
alias webbuild='set -a && source .env && set +a && node ./build-server/app.js'
alias changebase='git rebase --onto next-release test <FEATURE_BRANCH>'
alias gitlog='git log --oneline'
alias stash='git stash'
alias pop='git stash pop'
alias commit='git commit -S -m'
vpn() {
  /Users/<USER>/Github/cisco-autoconnect/cisco-autoconnect.sh $1
}

logon() {
  /Users/<USER>/Github/mac-pclog/sendEvents.sh logon $1 $2 $3
}

logoff() {
  /Users/<USER>/Github/mac-pclog/sendEvents.sh logoff $1 $2 $3
}

export NVM_DIR="$HOME/.nvm"
[ -s "/opt/homebrew/opt/nvm/nvm.sh" ] && \. "/opt/homebrew/opt/nvm/nvm.sh"                                       # This loads nvm
[ -s "/opt/homebrew/opt/nvm/etc/bash_completion.d/nvm" ] && \. "/opt/homebrew/opt/nvm/etc/bash_completion.d/nvm" # This loads nvm bash_completion

# run on first nvm installation
# nvm alias default node

# place this after nvm initialization!
autoload -U add-zsh-hook

load-nvmrc() {
  local nvmrc_path
  nvmrc_path="$(nvm_find_nvmrc)"

  if [ -n "$nvmrc_path" ]; then
    local nvmrc_node_version
    nvmrc_node_version=$(nvm version "$(cat "${nvmrc_path}")")

    if [ "$nvmrc_node_version" = "N/A" ]; then
      nvm install
    elif [ "$nvmrc_node_version" != "$(nvm version)" ]; then
      nvm use
    fi
  elif [ -n "$(PWD=$OLDPWD nvm_find_nvmrc)" ] && [ "$(nvm version)" != "$(nvm version default)" ]; then
    echo "Reverting to nvm default version"
    nvm use default
  fi
}

load-nvmrc
add-zsh-hook chpwd load-nvmrc

complete -C aws_completer aws
if [ ]; then
  source <(oc completion zsh)
  compdef _oc oc
fi
# export PATH="/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand:/opt/homebrew/bin:/opt/homebrew/bin"

# bun completions
[ -s "/Users/<USER>/.bun/_bun" ] && source "/Users/<USER>/.bun/_bun"

# bun
export BUN_INSTALL="$HOME/.bun"
export PATH="$BUN_INSTALL/bin:$PATH"
